<script setup>
definePage({
  name: "LoginExpired",
  meta: {
    title: "登录过期",
    navBar: false,
    isAuth: false,
  },
});
</script>

<template>
  <div class="expired-page">
    <div class="expired-container">
      <!-- 过期图标动画 -->
      <div class="icon-container">
        <div class="clock-container">
          <div class="clock">
            <div class="clock-face">
              <div class="clock-hour-hand" />
              <div class="clock-minute-hand" />
              <div class="clock-center" />
            </div>
            <div class="clock-shine" />
          </div>
          <div class="warning-icon">
            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round" />
            </svg>
          </div>
        </div>
      </div>

      <!-- 标题和描述 -->
      <div class="content">
        <h1 class="title">登录已过期</h1>
        <p class="message">您的登录状态已失效，请重新登录以继续使用</p>
        <div class="tips">
          <p>为了保护您的账户安全，系统会定期要求重新验证身份</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.expired-page {
  min-height: 100vh;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 30px;
  position: relative;
  overflow: hidden;
}

.expired-container {
  width: 100%;
  max-width: 690px;
  padding: 80px 48px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(40px);
  border-radius: 48px;
  box-shadow: 0 40px 80px rgba(0, 0, 0, 0.1);
  text-align: center;
  animation: slideUp 0.6s ease-out;

  @keyframes slideUp {
    0% {
      opacity: 0;
      transform: translateY(100px) scale(0.9);
    }
    100% {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }
}

.icon-container {
  margin-bottom: 60px;
  display: flex;
  justify-content: center;
  animation: iconFadeIn 0.8s ease-out;
  animation-delay: 0.3s;
  animation-fill-mode: both;

  @keyframes iconFadeIn {
    0% {
      opacity: 0;
      transform: translateY(-30px) scale(0.8);
    }
    100% {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }
}

.clock-container {
  position: relative;
  display: inline-block;
  animation: float 3s ease-in-out infinite;

  @keyframes float {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }
}

.clock {
  width: 160px;
  height: 160px;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  border-radius: 50%;
  position: relative;
  box-shadow: 0 20px 40px rgba(238, 90, 36, 0.3);
  animation: pulse 2s ease-in-out infinite;

  @keyframes pulse {
    0%,
    100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
  }
}

.clock-face {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: 4px solid rgba(255, 255, 255, 0.3);
}

.clock-hour-hand {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 6px;
  height: 30px;
  background: white;
  border-radius: 3px;
  transform-origin: bottom center;
  transform: translate(-50%, -100%) rotate(90deg);
  animation: hourRotate 4s linear infinite;

  @keyframes hourRotate {
    0% {
      transform: translate(-50%, -100%) rotate(90deg);
    }
    100% {
      transform: translate(-50%, -100%) rotate(450deg);
    }
  }
}

.clock-minute-hand {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 4px;
  height: 40px;
  background: white;
  border-radius: 2px;
  transform-origin: bottom center;
  transform: translate(-50%, -100%) rotate(180deg);
  animation: minuteRotate 2s linear infinite;

  @keyframes minuteRotate {
    0% {
      transform: translate(-50%, -100%) rotate(180deg);
    }
    100% {
      transform: translate(-50%, -100%) rotate(540deg);
    }
  }
}

.clock-center {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 12px;
  height: 12px;
  background: white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

.clock-shine {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, transparent 50%);
  border-radius: 50%;
}

.warning-icon {
  position: absolute;
  bottom: -10px;
  right: -10px;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 16px rgba(254, 202, 87, 0.4);
  animation: bounce 1s ease-in-out infinite;

  @keyframes bounce {
    0%,
    100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-5px);
    }
  }

  svg {
    width: 32px;
    height: 32px;
    color: white;
  }
}

.content {
  margin-bottom: 60px;

  .title {
    font-size: 76px;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 24px;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
    animation: titleSlideIn 0.8s ease-out;
    animation-delay: 0.5s;
    animation-fill-mode: both;

    @keyframes titleSlideIn {
      0% {
        opacity: 0;
        transform: translateY(-30px);
      }
      100% {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }

  .message {
    font-size: 36px;
    color: #4a5568;
    font-weight: 500;
    line-height: 1.5;
    margin-bottom: 32px;
    animation: messageSlideIn 0.8s ease-out;
    animation-delay: 0.7s;
    animation-fill-mode: both;

    @keyframes messageSlideIn {
      0% {
        opacity: 0;
        transform: translateY(20px);
      }
      100% {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }

  .tips {
    padding: 24px;
    background: rgba(240, 147, 251, 0.1);
    border-radius: 16px;
    border: 2px solid rgba(240, 147, 251, 0.2);
    animation: tipsSlideIn 0.8s ease-out;
    animation-delay: 0.9s;
    animation-fill-mode: both;

    @keyframes tipsSlideIn {
      0% {
        opacity: 0;
        transform: translateY(20px);
      }
      100% {
        opacity: 1;
        transform: translateY(0);
      }
    }

    p {
      font-size: 28px;
      color: #718096;
      margin: 0;
      line-height: 1.4;
    }
  }
}

// 移动端适配
@media (max-width: 640px) {
  .expired-page {
    padding: 20px 15px;
  }

  .expired-container {
    padding: 60px 32px;
    border-radius: 32px;
  }

  .clock {
    width: 120px;
    height: 120px;
  }

  .clock-face {
    width: 90px;
    height: 90px;
  }

  .warning-icon {
    width: 48px;
    height: 48px;

    svg {
      width: 24px;
      height: 24px;
    }
  }

  .content .title {
    font-size: 56px;
  }

  .content .message {
    font-size: 28px;
  }

  .tips p {
    font-size: 24px;
  }
}
</style>
